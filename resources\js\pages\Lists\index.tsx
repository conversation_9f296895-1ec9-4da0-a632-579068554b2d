import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type TaskList } from '@/types';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Lists',
        href: '/lists',
    },
];

interface ListsPageProps {
    lists: TaskList[];
}

export default function Lists({ lists }: ListsPageProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Lists">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="p-6">
                <div className="mb-6">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Task Lists</h1>
                    <p className="text-gray-600 dark:text-gray-400">Manage your task lists</p>
                </div>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {lists.map((list) => (
                        <div key={list.id} className="rounded-lg border border-gray-200 bg-white p-6 shadow-md dark:border-gray-700 dark:bg-gray-800">
                            <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">{list.title}</h3>
                            {list.description && <p className="mb-4 text-gray-600 dark:text-gray-400">{list.description}</p>}
                            <div className="text-sm text-gray-500 dark:text-gray-500">Created: {new Date(list.created_at).toLocaleDateString()}</div>
                        </div>
                    ))}
                </div>

                {lists.length === 0 && (
                    <div className="py-12 text-center">
                        <p className="text-gray-500 dark:text-gray-400">No task lists found. Create your first list!</p>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
